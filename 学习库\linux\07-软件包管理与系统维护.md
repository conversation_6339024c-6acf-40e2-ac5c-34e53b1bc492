---
tags:
  - 学习
  - linux
  - 软件包管理
  - 系统维护
---

# Linux软件包管理与系统维护

> [!info] 说明
> 本文档整理了Linux系统中软件包管理、系统维护和故障排查的常用命令和方法。

## 📦 软件包管理

### APT (Debian/Ubuntu)
| 命令 | 功能 | 示例 |
|------|------|------|
| `apt update` | 更新软件包列表 | `sudo apt update` |
| `apt upgrade` | 升级已安装软件 | `sudo apt upgrade` |
| `apt install` | 安装软件包 | `sudo apt install nginx` |
| `apt remove` | 卸载软件包 | `sudo apt remove nginx` |
| `apt purge` | 完全卸载(含配置) | `sudo apt purge nginx` |
| `apt search` | 搜索软件包 | `apt search python` |
| `apt show` | 显示软件包信息 | `apt show nginx` |
| `apt list` | 列出软件包 | `apt list --installed` |
| `apt autoremove` | 清理无用依赖 | `sudo apt autoremove` |
| `apt autoclean` | 清理软件包缓存 | `sudo apt autoclean` |

### YUM/DNF (CentOS/RHEL/Fedora)
| 命令 | 功能 | 示例 |
|------|------|------|
| `yum update` | 更新系统 | `sudo yum update` |
| `yum install` | 安装软件包 | `sudo yum install nginx` |
| `yum remove` | 卸载软件包 | `sudo yum remove nginx` |
| `yum search` | 搜索软件包 | `yum search python` |
| `yum info` | 显示软件包信息 | `yum info nginx` |
| `yum list` | 列出软件包 | `yum list installed` |
| `yum clean` | 清理缓存 | `sudo yum clean all` |
| `dnf` | Fedora新包管理器 | 用法同yum |

### 源码编译安装
```bash
# 典型的源码安装流程
wget https://example.com/software.tar.gz
tar -xzf software.tar.gz
cd software/

# 配置编译选项
./configure --prefix=/usr/local

# 编译
make

# 安装
sudo make install

# 卸载（如果支持）
sudo make uninstall
```

## 🔄 系统更新与维护

### Ubuntu换源配置

> [!warning] 操作前备份
> 在修改软件源之前，务必备份原始配置文件。

#### 1. 备份原始源文件
```bash
sudo cp /etc/apt/sources.list /etc/apt/sources.list.bak
```

#### 2. 编辑源文件
```bash
sudo vim /etc/apt/sources.list
```

> [!tip] Vim快速操作
> - 按 `ESC` 进入普通模式
> - 输入 `gg` 跳转到第一行
> - 按 `V` 进入可视化模式
> - 输入 `G` 选中全部内容
> - 按 `d` 删除所有内容

#### 3. 添加阿里云源
```bash
# Ubuntu 20.04 (Focal)
deb https://mirrors.aliyun.com/ubuntu/ focal main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-security main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-security main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-updates main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-updates main restricted universe multiverse

deb https://mirrors.aliyun.com/ubuntu/ focal-backports main restricted universe multiverse
deb-src https://mirrors.aliyun.com/ubuntu/ focal-backports main restricted universe multiverse
```

> [!info] 其他版本代号
> - Ubuntu 18.04: `bionic`
> - Ubuntu 22.04: `jammy`
> - Ubuntu 24.04: `noble`

#### 4. 更新软件包列表
```bash
sudo apt update && sudo apt upgrade
```

### 常用国内镜像源
| 镜像站  | 地址                           | 特点     |
| ---- | ---------------------------- | ------ |
| 阿里云  | mirrors.aliyun.com           | 速度快，稳定 |
| 清华大学 | mirrors.tuna.tsinghua.edu.cn | 教育网友好  |
| 中科大  | mirrors.ustc.edu.cn          | 老牌镜像站  |
| 华为云  | mirrors.huaweicloud.com      | 企业级稳定  |
| 网易   | mirrors.163.com              | 老牌商业镜像 |

## 🗜️ 压缩与归档

### tar命令详解
| 参数 | 功能 | 说明 |
|------|------|------|
| `-c` | 创建归档 | create |
| `-x` | 解压归档 | extract |
| `-t` | 列出内容 | list |
| `-v` | 详细输出 | verbose |
| `-f` | 指定文件名 | file |
| `-z` | gzip压缩 | |
| `-j` | bzip2压缩 | |
| `-J` | xz压缩 | |
| `-C` | 指定目录 | |

### 常用压缩操作
```bash
# 创建tar.gz归档
tar -czvf archive.tar.gz directory/

# 解压tar.gz归档
tar -xzvf archive.tar.gz

# 解压到指定目录
tar -xzvf archive.tar.gz -C /target/directory/

# 创建tar.bz2归档（更高压缩率）
tar -cjvf archive.tar.bz2 directory/

# 解压tar.bz2归档
tar -xjvf archive.tar.bz2

# 查看归档内容
tar -tzvf archive.tar.gz

# 追加文件到归档
tar -rvf archive.tar newfile.txt
```

### 其他压缩工具
| 命令 | 功能 | 示例 |
|------|------|------|
| `gzip` | 压缩文件 | `gzip file.txt` |
| `gunzip` | 解压gzip文件 | `gunzip file.txt.gz` |
| `zip` | 创建zip文件 | `zip -r archive.zip directory/` |
| `unzip` | 解压zip文件 | `unzip archive.zip -d /target/dir` |
| `7z` | 7zip压缩 | `7z a archive.7z directory/` |

> [!tip] 压缩格式选择
> - **tar.gz**: 通用性好，压缩速度快
> - **tar.bz2**: 压缩率高，速度较慢
> - **tar.xz**: 压缩率最高，速度最慢
> - **zip**: Windows兼容性好
> - **7z**: 压缩率很高，需要额外安装

## 🔧 系统服务管理

### systemctl命令
| 命令 | 功能 | 示例 |
|------|------|------|
| `systemctl status` | 查看服务状态 | `systemctl status nginx` |
| `systemctl start` | 启动服务 | `sudo systemctl start nginx` |
| `systemctl stop` | 停止服务 | `sudo systemctl stop nginx` |
| `systemctl restart` | 重启服务 | `sudo systemctl restart nginx` |
| `systemctl reload` | 重载配置 | `sudo systemctl reload nginx` |
| `systemctl enable` | 开机自启 | `sudo systemctl enable nginx` |
| `systemctl disable` | 禁用自启 | `sudo systemctl disable nginx` |
| `systemctl is-active` | 检查是否运行 | `systemctl is-active nginx` |
| `systemctl is-enabled` | 检查是否自启 | `systemctl is-enabled nginx` |

### 服务管理示例
```bash
# 查看所有服务状态
systemctl list-units --type=service

# 查看失败的服务
systemctl --failed

# 查看服务日志
journalctl -u nginx -f

# 查看服务配置文件位置
systemctl show nginx | grep FragmentPath

# 重载systemd配置
sudo systemctl daemon-reload
```

## 📊 系统监控与维护

### 磁盘空间管理
```bash
# 查看磁盘使用情况
df -h

# 查看目录大小
du -sh /var/log/*

# 查找大文件
find / -type f -size +100M 2>/dev/null

# 清理系统日志
sudo journalctl --vacuum-time=7d
sudo journalctl --vacuum-size=100M

# 清理APT缓存
sudo apt clean
sudo apt autoclean
sudo apt autoremove

# 清理临时文件
sudo rm -rf /tmp/*
sudo rm -rf /var/tmp/*
```

### 内存管理
```bash
# 查看内存使用
free -h

# 清理缓存
sudo sync
echo 3 | sudo tee /proc/sys/vm/drop_caches

# 查看交换分区使用
swapon --show

# 临时禁用交换分区
sudo swapoff -a

# 重新启用交换分区
sudo swapon -a
```

### 进程管理
```bash
# 查看系统负载
uptime
top
htop

# 查找占用资源最多的进程
ps aux --sort=-%cpu | head -10
ps aux --sort=-%mem | head -10

# 杀死僵尸进程
ps aux | grep -w Z
kill -9 $(ps aux | grep -w Z | awk '{print $2}')

# 查看进程树
pstree -p
```

## 🔍 系统故障排查

### 日志分析
```bash
# 系统日志
sudo tail -f /var/log/syslog          # Debian/Ubuntu
sudo tail -f /var/log/messages        # CentOS/RHEL

# 认证日志
sudo tail -f /var/log/auth.log        # Debian/Ubuntu
sudo tail -f /var/log/secure          # CentOS/RHEL

# 内核日志
dmesg | tail -20
sudo tail -f /var/log/kern.log

# systemd日志
journalctl -f                         # 实时查看
journalctl -u service_name            # 查看特定服务
journalctl --since "1 hour ago"       # 查看最近1小时
journalctl --until "2023-01-01"       # 查看指定时间前
```

### 网络故障排查
```bash
# 检查网络连接
ping -c 4 *******
ping -c 4 google.com

# 检查DNS解析
nslookup google.com
dig google.com

# 检查路由
ip route show
traceroute google.com

# 检查端口
netstat -tulnp | grep :80
ss -tulnp | grep :80

# 检查防火墙
sudo ufw status                       # Ubuntu
sudo firewall-cmd --list-all          # CentOS/RHEL
```

### 性能问题排查
```bash
# CPU使用率
top -bn1 | grep "Cpu(s)"
sar -u 1 5

# 内存使用
free -h
cat /proc/meminfo

# 磁盘I/O
iostat -x 1 5
iotop

# 网络I/O
iftop
nethogs
```

## 🛠️ 系统维护脚本

### 系统清理脚本
```bash
#!/bin/bash
# 系统清理脚本

echo "开始系统清理..."

# 更新软件包列表
echo "更新软件包列表..."
sudo apt update

# 清理APT缓存
echo "清理APT缓存..."
sudo apt autoclean
sudo apt autoremove -y

# 清理日志文件
echo "清理系统日志..."
sudo journalctl --vacuum-time=7d

# 清理临时文件
echo "清理临时文件..."
sudo rm -rf /tmp/*
sudo rm -rf /var/tmp/*

# 清理缩略图缓存
echo "清理缩略图缓存..."
rm -rf ~/.cache/thumbnails/*

# 显示清理结果
echo "清理完成！"
df -h
```

### 系统备份脚本
```bash
#!/bin/bash
# 系统备份脚本

BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="system_backup_$DATE.tar.gz"

echo "开始系统备份..."

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份重要目录
tar -czf $BACKUP_DIR/$BACKUP_FILE \
    --exclude=/proc \
    --exclude=/sys \
    --exclude=/dev \
    --exclude=/tmp \
    --exclude=/var/tmp \
    --exclude=/backup \
    /etc /home /var/log

echo "备份完成: $BACKUP_DIR/$BACKUP_FILE"
ls -lh $BACKUP_DIR/$BACKUP_FILE
```

### 系统监控脚本
```bash
#!/bin/bash
# 系统监控脚本

echo "系统监控报告 - $(date)"
echo "================================"

# 系统信息
echo "系统信息:"
uname -a

# 运行时间和负载
echo -e "\n运行时间和负载:"
uptime

# 内存使用
echo -e "\n内存使用:"
free -h

# 磁盘使用
echo -e "\n磁盘使用:"
df -h | grep -E "^/dev/"

# CPU使用率最高的进程
echo -e "\nCPU使用率最高的进程:"
ps aux --sort=-%cpu | head -5

# 内存使用最高的进程
echo -e "\n内存使用最高的进程:"
ps aux --sort=-%mem | head -5

# 网络连接
echo -e "\n网络连接统计:"
ss -s
```



> [!success] 系统维护最佳实践
> 1. **定期备份**: 建立自动化备份机制
> 2. **监控告警**: 设置关键指标的监控告警
> 3. **文档记录**: 记录维护操作和配置变更
> 4. **测试恢复**: 定期测试备份和恢复流程
> 5. **安全更新**: 及时安装安全补丁和更新

---

**相关笔记链接:**
- [[03-系统信息与进程管理]]
- [[05-权限与安全管理]]
- [[shell]]
