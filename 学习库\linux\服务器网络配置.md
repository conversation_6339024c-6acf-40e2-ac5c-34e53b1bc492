---
tags:
  - 学习
  - linux
  - 服务器配置
  - 科学上网
---
# 服务器

```ad-info
使用服务器可以获得更好，且更快的训练，本文介绍了有关于服务器中的网络知识
```

---

## 使用镜像

使用镜像的方式可以获得更稳定的下载服务，以下是一些常用的镜像地址：

|             | 原网址                     | 镜像地址                   |
| ----------- | ----------------------- | ---------------------- |
| huggingface | https://huggingface.co/ | https://hf-mirror.com/ |
| github      | https://github.com      | https://bgithub.xyz    |
|             |                         |                        |

此外，在使用pip安装Python包时，可以进行 [[07-软件包管理与系统维护#🔄 系统更新与维护|换源]]。

## 使得 wsl 访问
### clash中的操作 

在 clash 中要开启局域网连接，并且记住端口号用于后面的操作
![](./attachments/服务器网络配置-2025-08-04,22-08,22-56-51.webp)

### windows 中的操作

````ad-info
WSL [2.2.1 版本](https://github.com/microsoft/WSL/releases/tag/2.2.1)以后默认启用了 DNS 隧道, 会导致**该方法**失效。

参照[官方文档](https://learn.microsoft.com/zh-cn/windows/wsl/wsl-config#wslconfig)配置，在windows中 `C:\Users\<USER>\.wslconfig` 文件中 (如果不存在就手动创建一个) 加入以下内容以关闭 DNS 隧道:

```shell
[wsl2]  
dnsTunneling=false
```

可以执行 `wsl -v` 查看你的 WSL 版本。
````

### wsl 中的操作

在 wsl 中进行以下操作，端口号在 clash 中进行查看，完成后输入 `source ~/.bashrc` 使文件修改生效，可以通过输入 `echo $http_proxy` 验证修改是否成功。
```bash
nano ~/.bashrc
host_ip=$(cat /etc/resolv.conf |grep "nameserver" |cut -f 2 -d " ")  
export http_proxy="http://$host_ip:[端口]"  
export https_proxy="http://$host_ip:[端口]"
```

在使用 `sudo` 执行命令时，之前设置的环境变量并不会生效，需要进一步设置。  
取消相应行的注释 (删除行首的 `#`) 即可.
![](./attachments/服务器网络配置-2025-08-04,22-08,22-52-18.webp)

输入 `wget www.google.com`, 可以发现连接成功，至此大功告成。
![](./attachments/服务器网络配置-2025-08-04,22-08,22-51-47.webp)

## 使远程服务器访问

本地的电脑运行这 clash 网络代理，其监听在 `localhost:7897` 这个端口，但由于没有公网 ip，互联网上的其它设备无法直接访问这个服务，因此可以通过**反向 SSH 隧道**来实现，可以理解为：
- **本地电脑**：你的家，有一部内部电话（`localhost:7897`），但没有对外公布的号码。
- **远程服务器**：一家拥有公共总机号码（服务器的公网IP）的大公司。
- **反向SSH隧道命令**：你从家里主动给公司总机打电话，对前台说：“你好，请帮我开通一条专线。以后任何打到公司总机并要找 **7897分机** （这里可以设置为任意==服务器能对外开放且未被使⽤的端⼝==）的电话，都请**直接转接到我家里这部内部电话上**。
这条“专线”就是反向SSH隧道。它由你的本地电脑**主动发起**，但在远程服务器上**被动地监听**一个端口，并将所有流量转发回你的本地电脑。

需要使用的命令如下：
```shell
ssh -NfR <服务器公网端口>:<本地服务地址>:<本地服务端口> user@你的服务器IP

# 例如
ssh -NfR 7897:localhost:7897 root@**************  
# 这里远程服务器端口为 7897，本地服务地址和服务端口为 localhost:7897，远程服务器的用户和地址为root@**************
```

### 检测远程服务器端口是否开放

#### 本地检测

在**本地**可以通过 `telnet` 来检测远程服务器的某一端口是否开放，实现开启 windows 的 telnet 功能，可以**以管理员身份运行 powershell**执行:
```shell
dism /online /enable-feature /featurename:TelnetClient
```

再进行检测：
```shell
telnet [服务器IP地址或域名] [端口号]

# 例如检查远程服务器的7897端口
telnet 110.41.************ 
```

- **成功**: 屏幕变黑，并显示类似 Connected to ***************. 的信息。这表示端口是开放的。（按 Ctrl + ] 然后输入 quit 退出）。
- **失败**: 立即显示 Connection refused -> 端口关闭。
- **超时**: 长时间没有响应 -> 端口被防火墙过滤。

#### 远程服务器检测

如果**能够登录远程服务器**，也能够**在远程服务器的内部**进行检测，使用 `ss` 来进行检测：
```bash
sudo ss -tlpn | grep ':[端口号]'  # 这里如果是以root用户登录的话就不用夹sudo了

# 例如检查7897端口
ss -tlpn | grep ':7897'
```

- -t: TCP 协议
- -l: 监听 (Listening) 状态的端口
- -p: 显示监听端口的进程
- -n: 以数字形式显示端口号

如果输出了类似这种形式:
```
LISTEN 0      128          0.0.0.0:7897      0.0.0.0:*    users:(("sshd",pid=2897,fd=5))
``` 

表示为有一个 pid 为 2897 的进程正在监听 7897 端口（因为这时我们的本地还没有连接，为了避免被占用所以我们需要 `kill [pid号]` 来将这个进程该杀死），这里的 **0.0.0.0 表示监听所有的端口**，**可以从外部访问**，如果是 **127.0.0.1**表示只监听本地回环地址，**无法从外部访问**。
如果没有输出表示为没有任何程序正在监听这个端口，我们现在**需要的就是没有进程监听**。

>如果是自己的服务器（比如华为云，阿里云）可以找到安全组配置，添加入栈规则允许 TCP 协议的 7897 端口被任何IP (0.0.0.0/0) 访问。然后在服务器中执行命令 `ufw allow 7897`，在系统防火墙中也允许该端口。

### 通道转发

假如上面已经确定了远程服务器了 `7897` 端口已经是**开放并且没有被监听**，我们需要再[[服务器网络配置#clash中的操作|确定一下clash中是否运行局域网连接，以及端口号是多少]]，这里我们的 clash 端口号也为 `7897`，在本地执行以下命令用以测试是否成功：
```shell
ssh -v -NR 7897:localhost:7897 root@**************
```

- -v: 启用详细输出。
- 没有加-f：让它在前台运行，看到所有调试信息。

如果出现了如下面的，则表示失败了：
```shell
debug1: Remote: Forwarding listen address "localhost" overridden by server GatewayPorts
debug1: remote forward failure for: listen 7897, connect localhost:7897
Warning: remote port forwarding failed for listen port 7897
```

需要编辑 `sshd_config` 文件
```bash
nano /etc/ssh/sshd_config
```

在文件中找到 `GatewayPorts` 和 `AllowTcpForwarding`，并确保他们的值为 yes。
![](./attachments/服务器网络配置-2025-08-05,11-08,11-29-43.webp)

保存并退出后，使用 `systemctl restart sshd` 重启 ssh（最好也重启下服务器）。然后使用以下来进行转发，并且保持后台运行：
```bash
ssh -NfR 7897:localhost:7897 root@你的服务器IP
```

* `-N`: 不执行远程命令，只用于端口转发。
* `-f`: 在后台运行。 
* `-R 7897:localhost:7897`: 将**远程服务器**的`7897`端口，转发到**本地**的`localhost:7897`。

这时候再开启一个窗口执行普通的 ssh，并运行 `ss -tlpn | grep ':7897'`，如果能看到一个 `sshd` 进程正在监听 `0.0.0.0:7897`，说明隧道入口已开启。然后可以在这个远程服务器上执行以下来配置转发：
```bash
export https_proxy=http://127.0.0.1:7890 
export http_proxy=http://127.0.0.1:7890
```

可以通过 `curl www.google.com` 来进行测试，如果有输出则表示成功，可以下载某些被限制的资源了，下载完成后可以使用以下来取消转发：
```shell
unset http_proxy 
unset https_proxy
```
